// 监管账号管理列表、弹框项
import validate from '@/utils/validate';

export default {
  index: true,
  indexLabel: '序号',
  rowKey: 'id',
  reserveSelection: true,
  selection: true,
  align: 'center',
  card: true,
  menuAlign: 'center',
  emptyBtnIcon: 'el-icon-refresh',
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  addTitle: '新增监管账号',
  viewTitle: '监管账号详情',
  editTitle: '修改监管账号',
  editBtnText: '修改',
  updateBtnText: '保存',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  addBtnPermission: 'admin:xzp:acctmgmt:add',
  editBtnPermission: 'admin:xzp:acctmgmt:edit',
  delBtnPermission: 'admin:xzp:acctmgmt:remove',
  searchBtn: true,
  refreshBtn: true,
  emptyBtn: true,
  labelWidth: 120,
  labelPosition: 'right',
  tip: false,
  columnBtn: true,
  menuWidth: 200,
  column: [
    {
      label: 'ID',
      prop: 'id',
      hide: true,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '委托单位代码',
      prop: 'merchId',
      type: 'autocomplete',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      placeholder: '请输入委托单位代码',
      clearable: true,
      triggerOnFocus: true,
      slot: true,
      formslot: true,
      searchslot: true,
      rules: [{
        required: true,
        message: '请输入委托单位代码',
        trigger: 'blur'
      }, {
        max: 32,
        message: '委托单位代码不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '业务代码',
      prop: 'opeCd',
      type: 'autocomplete',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      placeholder: '请输入业务代码',
      clearable: true,
      triggerOnFocus: true,
      slot: true,
      formslot: true,
      searchslot: true,
      rules: [{
        required: true,
        message: '请输入业务代码',
        trigger: 'blur'
      }, {
        max: 32,
        message: '业务代码不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '监管账户',
      prop: 'cpabAccId',
      width: 180,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        required: true,
        message: '请输入公司账户',
        trigger: 'blur'
      }, {
        max: 32,
        message: '公司账户不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '户名',
      prop: 'acctNm',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        required: true,
        message: '请输入户名',
        trigger: 'blur'
      }, {
        max: 128,
        message: '户名不能超过128个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '业务类型',
      prop: 'transType',
      width: 100,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      value: 'zzg'
    },
    {
      label: '开户机构号',
      prop: 'openBrhId',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        max: 32,
        message: '开户机构号不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '开户机构名称',
      prop: 'brhName',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 128,
        message: '开户机构名称不能超过128个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '人行机构号',
      prop: 'pbcBrhId',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        max: 32,
        message: '人行机构号不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '交易日期',
      prop: 'tranDt',
      type: 'date',
      format: 'yyyyMMdd',
      valueFormat: 'yyyyMMdd',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 8,
        message: '交易日期格式为YYYYMMDD',
        trigger: 'blur'
      }]
    },
    {
      label: '证件类型',
      prop: 'paperType',
      width: 100,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 8,
        message: '证件类型不能超过8个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '证件号码',
      prop: 'paperId',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 32,
        message: '证件号码不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '关联账号',
      prop: 'peerAccId',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        max: 32,
        message: '关联账号不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '关联户名',
      prop: 'dtlCstmNm',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      search: true,
      searchLabelWidth: 120,
      searchSpan: 6,
      rules: [{
        max: 128,
        message: '关联户名不能超过128个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '关联账户类型',
      prop: 'acctType',
      type: 'select',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      dicData: [
        { label: '同行', value: '1' },
        { label: '跨行', value: '2' }
      ],
      rules: [{
        max: 8,
        message: '关联账户类型不能超过8个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '交易机构',
      prop: 'txnBrhId',
      width: 120,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 32,
        message: '交易机构不能超过32个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '交易时间戳',
      prop: 'tranTime',
      type: 'datetime',
      format: 'yyyyMMddHHmmss',
      valueFormat: 'yyyyMMddHHmmss',
      width: 150,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 14,
        message: '交易时间戳格式为YYYYMMDDHHMMSS',
        trigger: 'blur'
      }]
    },
    {
      label: '操作员',
      prop: 'tlrId',
      width: 100,
      showColumn: true,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 16,
        message: '操作员不能超过16个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '附加字段1',
      prop: 'othMsg1Tx',
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 255,
        message: '附加字段1不能超过255个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '附加字段2',
      prop: 'othMsg2Tx',
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 255,
        message: '附加字段2不能超过255个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '附加字段3',
      prop: 'othMsg3Tx',
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 255,
        message: '附加字段3不能超过255个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '附加字段4',
      prop: 'othMsg4Tx',
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 255,
        message: '附加字段4不能超过255个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '附加字段5',
      prop: 'othMsg5Tx',
      width: 120,
      showColumn: false,
      addDisplay: true,
      editDisplay: true,
      span: 12,
      rules: [{
        max: 255,
        message: '附加字段5不能超过255个字符',
        trigger: 'blur'
      }]
    },
    {
      label: '创建人',
      prop: 'createBy',
      width: 100,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true
    },
    {
      label: '更新人',
      prop: 'updateBy',
      width: 100,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      width: 150,
      showColumn: false,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true
    }
  ]
};